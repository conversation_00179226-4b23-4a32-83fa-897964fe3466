{"version": 1.1, "atDirectives": [{"name": "@tailwind", "description": "Use the @tailwind directive to insert Tailwind's base, components, utilities and variants styles into your CSS.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#tailwind"}]}, {"name": "@apply", "description": "Use the @apply directive to inline any existing utility classes into your own custom CSS.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#apply"}]}, {"name": "@layer", "description": "Use the @layer directive to tell Tailwind which \"bucket\" a set of custom styles belong to.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#layer"}]}], "properties": [{"name": "line-clamp", "description": "The line-clamp CSS property allows limiting of the contents of a block container to the specified number of lines.", "browsers": ["C90", "FF68"], "syntax": "none | <integer>", "relevance": 50, "references": [{"name": "MDN Reference", "url": "https://developer.mozilla.org/docs/Web/CSS/line-clamp"}]}]}