{"css.validate": false, "scss.validate": false, "less.validate": false, "css.lint.unknownAtRules": "ignore", "scss.lint.unknownAtRules": "ignore", "less.lint.unknownAtRules": "ignore", "tailwindCSS.includeLanguages": {"typescript": "typescript", "javascript": "javascript", "typescriptreact": "typescriptreact", "javascriptreact": "javascriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "editor.quickSuggestions": {"strings": true}, "css.customData": [".vscode/css_custom_data.json"]}