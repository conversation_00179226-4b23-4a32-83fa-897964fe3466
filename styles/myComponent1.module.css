.deconstructed {
    position: relative;
    margin: auto;
    font-family: 'Cambay', sans-serif;
    font-size: 30px;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: transparent;
    height: 1em;
}

.deconstructed > div {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(
        135deg,
        rgb(139, 92, 246) 0%,
        rgb(192, 38, 211) 25%,
        rgb(219, 39, 119) 50%,
        rgb(59, 130, 246) 75%,
        rgb(139, 92, 246) 100%
    );
    background-size: 400% 400%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: holographicShift 12s linear infinite;
    filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.3));
}

@keyframes holographicShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.deconstructed > div:nth-child(1) {
    opacity: 1;
    transform: translateX(0);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    animation: holographicShift 12s linear infinite, glitchEffect 6s infinite step-end;
}

.deconstructed > div:nth-child(2) {
    opacity: 0.9;
    transform: translateX(0);
    filter: hue-rotate(15deg) drop-shadow(0 0 2px rgba(192, 38, 211, 0.5));
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    animation: holographicShift 12s linear infinite, glitchEffect 6s infinite step-end;
    animation-delay: 0.1s;
}

.deconstructed > div:nth-child(3) {
    opacity: 0.8;
    transform: translateX(0);
    filter: hue-rotate(30deg) drop-shadow(0 0 2px rgba(219, 39, 119, 0.5));
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    animation: holographicShift 12s linear infinite, glitchEffect 6s infinite step-end;
    animation-delay: 0.2s;
}

.deconstructed > div:nth-child(4) {
    opacity: 0.7;
    transform: translateX(0);
    filter: hue-rotate(45deg) drop-shadow(0 0 2px rgba(59, 130, 246, 0.5));
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    animation: holographicShift 12s linear infinite, glitchEffect 6s infinite step-end;
    animation-delay: 0.3s;
}

@keyframes glitchEffect {
    0%, 85% {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
        transform: translate(0);
        filter: brightness(1);
    }
    86% {
        clip-path: polygon(0 15%, 100% 15%, 100% 80%, 0 80%);
        transform: translate(-3px, 2px);
        filter: brightness(1.2) contrast(1.3);
    }
    88% {
        clip-path: polygon(0 10%, 100% 10%, 100% 75%, 0 75%);
        transform: translate(4px, -3px);
        filter: brightness(1.1) hue-rotate(10deg);
    }
    90% {
        clip-path: polygon(0 30%, 100% 30%, 100% 65%, 0 65%);
        transform: translate(4px, 1px);
        filter: brightness(1.3) contrast(1.2);
    }
    92% {
        clip-path: polygon(0 30%, 100% 30%, 100% 90%, 0 90%);
        transform: translate(-4px, 3px);
        filter: brightness(0.9) hue-rotate(-10deg);
    }
    94% {
        clip-path: polygon(0 5%, 100% 5%, 100% 70%, 0 70%);
        transform: translate(-6px, -2px);
        filter: brightness(1.2) contrast(1.4);
    }
    96% {
        clip-path: polygon(0 20%, 100% 20%, 100% 85%, 0 85%);
        transform: translate(6px, 2px);
        filter: brightness(1.1) hue-rotate(20deg);
    }
    98% {
        clip-path: polygon(0 15%, 100% 15%, 100% 75%, 0 75%);
        transform: translate(-3px, -1px);
        filter: brightness(1.3) contrast(1.1);
    }
    100% {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
        transform: translate(0);
        filter: brightness(1);
    }
}